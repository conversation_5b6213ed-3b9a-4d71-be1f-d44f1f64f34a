import NotificationBadge from "@/components/NotificationBadge";
import { getModifierText, getStatusEffectDetails, statusEffects } from "@/helpers/statusEffects";
import { getReturnValues } from "@/hooks/useCountdown";
import { cn } from "@/lib/utils";
import { type StatusEffect } from "@/hooks/api/useGetStatusEffects";

interface StatusEffectsProps {
    type?: "player" | string;
    currentEffects: Record<string, any> | null | undefined;
    isBattle?: boolean;
    className?: string;
}

const StatusEffects = ({ type = "player", currentEffects, isBattle = false, className }: StatusEffectsProps) => {
    if (!currentEffects) return null;

    const activeEffects: StatusEffect[] = [];

    for (const [key, value] of Object.entries(currentEffects)) {
        let statusEffect: StatusEffect | null = null;

        if (isBattle) {
            statusEffect = getStatusEffectDetails(key, value);

            if (value.turns) {
                statusEffect.turns = value.turns;
            }
        } else {
            const effect = value.effect;

            statusEffect = {
                ...effect,
                count: value.stacks,
                endsAt: value?.endsAt,
                customName: value.customName,
                ...statusEffects[effect?.source],
            };
        }
        if (statusEffect) {
            if (isBattle) {
                activeEffects.push({ value: value, ...statusEffect });
            } else {
                activeEffects.push(statusEffect);
            }
        }
    }

    const formatTime = (timestamp: string | undefined): string | null => {
        if (timestamp) {
            const date = new Date(timestamp);
            const formatted = date.getTime() - new Date().getTime();
            const values = getReturnValues(formatted);
            return `${Number.parseInt(values[1])}h  ${values[2]}m`;
        }
        return null;
    };

    return (
        <div
            className={cn(
                "mx-3 mb-1 flex flex-row md:mb-0 2xl:gap-3.5",
                type !== "player" ? "flex-row-reverse" : "flex-row",
                isBattle ? "gap-4" : "gap-3",
                className
            )}
        >
            {activeEffects.map((effect, i) => (
                <div
                    key={effect.id ? effect.id : i}
                    data-tooltip-id="statuseffect-tooltip"
                    data-effect-name={effect.customName ? effect.customName : effect.name}
                    data-effect-turns={effect.hideTurns ? null : effect?.turns}
                    data-effect-description={effect.description ? effect.description : null}
                    data-effect-tier={effect.tier ? effect.tier : null}
                    data-effect-source={effect?.source ? effect?.source : null}
                    data-effect-amount={effect.count ? effect.count : null}
                    data-effect-endsat={effect.endsAt ? formatTime(effect.endsAt) : null}
                    className="relative"
                    data-effect-modifier={
                        effect.modifier && effect.modifier !== 0
                            ? getModifierText(effect.modifier, effect?.source)
                            : null
                    }
                >
                    {effect.count && effect.count > 0 && (
                        <div className="-bottom-1 -right-2 absolute flex size-4 items-center justify-center rounded-full border border-black bg-red-500 font-body font-semibold text-[0.7rem] text-stroke-sm text-white">
                            {effect.count}
                        </div>
                    )}

                    <img
                        src={effect.icon}
                        alt=""
                        className={cn(
                            effect?.type === "buff" ? "ring-sky-500" : "ring-red-500",
                            "mt-1 h-8 w-auto rounded-md ring-2 md:h-7 2xl:h-8"
                        )}
                    />
                    {isBattle && !effect.hideTurns && effect?.turns > 0 ? (
                        <NotificationBadge amount={effect?.turns} className="-bottom-2 -end-3 size-5!" />
                    ) : null}
                </div>
            ))}
        </div>
    );
};

export default StatusEffects;
