import asleepImg from "@/assets/icons/statuseffects/asleep.png";
import bleedImg from "@/assets/icons/statuseffects/bleed.png";
import concussionImg from "@/assets/icons/statuseffects/concussion.png";
import crippledImg from "@/assets/icons/statuseffects/crippled.png";
import disarmedImg from "@/assets/icons/statuseffects/disarmed.png";
import enragedImg from "@/assets/icons/statuseffects/enraged.png";
import exhaustedImg from "@/assets/icons/statuseffects/exhausted.png";
import fatigueImg from "@/assets/icons/statuseffects/fatigue.png";
import frenziedImg from "@/assets/icons/statuseffects/frenzied.png";
import guardingImg from "@/assets/icons/statuseffects/guarding.png";
import painImg from "@/assets/icons/statuseffects/pain.png";
import poisonedImg from "@/assets/icons/statuseffects/poisoned.png";
import recoveryImg from "@/assets/icons/statuseffects/recovery.png";
import abilityLockImg from "@/assets/icons/statuseffects/abilityLock.png";
import stunnedImg from "@/assets/icons/statuseffects/stunned.png";
import traumaImg from "@/assets/icons/statuseffects/trauma.png";

interface CombatEffect {
    name: string;
    icon: string;
    description: string;
    hideTurns?: boolean;
    type?: string;
}

export const getStatusEffectDetails = (key: string, value: number) => {
    const combatEffects: Record<string, Record<string, CombatEffect>> = {
        buffs: {
            enraged: { name: "Enraged", icon: enragedImg, description: "" },
            recovery: { name: "Recovery", icon: recoveryImg, description: "" },
            frenzied: { name: "Frenzied", icon: frenziedImg, description: "" },
            guarding: { name: "Guarding", icon: guardingImg, description: "" },
            antiMelee: {
                name: "Guarding",
                icon: guardingImg,
                description: "Increased protection against melee attacks",
                hideTurns: true,
            },
            antiRanged: {
                name: "Guarding",
                icon: guardingImg,
                description: "Increased protection against ranged attacks",
                hideTurns: true,
            },
            antiAttack: {
                name: "Guarding",
                icon: guardingImg,
                description: "Increased protection against normal attacks",
                hideTurns: true,
            },
            staminaBuffAttack: {
                name: "Anti Stam",
                icon: frenziedImg,
                description: "Deals bonus damage based on how low their opponents stamina is",
                hideTurns: true,
            },
            npcEnrage: {
                name: "Short Fuse",
                icon: frenziedImg,
                description: "Damage increases every turn",
                hideTurns: true,
            },
            npcRecovery: {
                name: "Recovery",
                icon: recoveryImg,
                description: "Healing over time",
                hideTurns: true,
            },
            damageBuff: {
                name: "Damage Buff",
                icon: enragedImg,
                description: `+ ${(value * 100).toFixed(0) || "0"}% Damage`,
                hideTurns: true,
                type: "buff",
            },
            strengthBuff: {
                name: "Strength Buff",
                icon: enragedImg,
                description: `+ ${(value * 100).toFixed(0) || "0"}% Strength`,
                hideTurns: true,
                type: "buff",
            },
            dexterityBuff: {
                name: "Dexterity Buff",
                icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/YwXXfqH.png`,
                description: `+ ${(value * 100).toFixed(0) || "0"}% Dexterity`,
                hideTurns: true,
                type: "buff",
            },
            defenceBuff: {
                name: "Defence Buff",
                icon: guardingImg,
                description: `+ ${(value * 100).toFixed(0) || "0"}% Defence`,
                hideTurns: true,
                type: "buff",
            },
            staminaBuff: {
                name: "Stamina Buff",
                icon: frenziedImg,
                description: `+ ${value || "0"} Max Stamina`,
                hideTurns: true,
                type: "buff",
            },
            zombieBuff: {
                name: "Fresh Brains",
                icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/2LYGQTA.png`,
                description: `+ ${(value * 100).toFixed(0) || "0"}% Damage`,
                hideTurns: true,
                type: "buff",
            },
        },
        debuffs: {
            crippled: { name: "Crippled", icon: crippledImg, description: "" },
            stunned: { name: "Stunned", icon: stunnedImg, description: "" },
            exhausted: { name: "Exhausted", icon: exhaustedImg, description: "" },
            asleep: { name: "Asleep", icon: asleepImg, description: "" },
            poisoned: { name: "Poisoned", icon: poisonedImg, description: "" },
            disarmed: { name: "Disarmed", icon: disarmedImg, description: "" },
            npcWeakened: {
                name: "Weakened",
                icon: crippledImg,
                description: "Defence reduces every turn",
                hideTurns: true,
            },
            abilityLock: {
                name: "Ability Locked",
                icon: abilityLockImg,
                description: "Can't use abilities",
                hideTurns: true,
            },
            abilityLockShowTurns: {
                name: "Ability Locked",
                icon: abilityLockImg,
                description: "Can't use abilities",
            },
            deepSleep: {
                name: "Deep Sleep",
                icon: asleepImg,
                description: "Asleep..",
            },
            npcPoison: {
                name: "Poisoned",
                icon: poisonedImg,
                description: "Taking poison damage each turn",
                hideTurns: true,
            },

            // Injury Debuffs
            bleed: {
                name: "Bleeding",
                icon: bleedImg,
                description: `Taking ${(value * 100)?.toFixed(0) || "0"}% max HP damage each turn`,
                hideTurns: true,
            },
            damage: {
                name: "Fracture",
                icon: exhaustedImg,
                description: `Damage is reduced by ${(value * 100).toFixed(0) || "0"}%`,
                hideTurns: true,
            },
            abilityDamage: {
                name: "Trauma",
                icon: traumaImg,
                description: `Ability Damage is reduced by ${(value * 100).toFixed(0) || "0"}%`,
                hideTurns: true,
            },
            defence: {
                name: "Contusion",
                icon: crippledImg,
                description: `Defence is reduced by ${(value * 100).toFixed(0) || "0"}%`,
                hideTurns: true,
            },
            staminaRegen: {
                name: "Fatigue",
                icon: fatigueImg,
                description: `Stamina regen is reduced by 100% due to fatigue`,
                hideTurns: true,
            },
            accuracy: {
                name: "Concussion",
                icon: concussionImg,
                description: `Attacks have a ${(value * 100)?.toFixed(0) || "0"}% chance to miss`,
                hideTurns: true,
            },
            injuryAbilityLock: {
                name: "Heavy Fatigue",
                icon: abilityLockImg,
                description: `Can't use abilities`,
                hideTurns: true,
            },
            zombie: {
                name: "Zombified",
                icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/WWSOgR8.png`,
                description: `- ${(value * 100).toFixed(0) || "0"}% Defence`,
                hideTurns: true,
            },
            fever: {
                name: "Fever",
                icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/2LYGQTA.png`,
                description: `A raging headache`,
                hideTurns: true,
            },
        },
    };

    const battleStatusEffects: Record<string, CombatEffect> = {
        // Buffs
        rage: combatEffects.buffs.enraged,
        heal_over_time: combatEffects.buffs.recovery,
        self_harm: combatEffects.buffs.frenzied,
        high_guard: combatEffects.buffs.guarding,
        anti_melee: combatEffects.buffs.antiMelee,
        anti_ranged: combatEffects.buffs.antiRanged,
        anti_attack: combatEffects.buffs.antiAttack,
        zombiedmg_buff: combatEffects.buffs.zombieBuff,
        damage_buff: combatEffects.buffs.damageBuff,
        strength_buff: combatEffects.buffs.strengthBuff,
        dexterity_buff: combatEffects.buffs.dexterityBuff,
        defence_buff: combatEffects.buffs.defenceBuff,
        stamina_buff: combatEffects.buffs.staminaBuff,

        // NPC Buffs
        npc_heal_over_time: combatEffects.buffs.npcRecovery,
        npc_stamina_buff_attack: combatEffects.buffs.staminaBuffAttack,
        npc_enrage: combatEffects.buffs.npcEnrage,

        // Debuffs
        cripple: combatEffects.debuffs.crippled,
        stun: combatEffects.debuffs.stunned,
        shockwave: combatEffects.debuffs.stunned,
        exhaust: combatEffects.debuffs.exhausted,
        sleep: combatEffects.debuffs.asleep,
        sap_sleep: combatEffects.debuffs.asleep,
        toxic_dart: combatEffects.debuffs.poisoned,
        disarm: combatEffects.debuffs.disarmed,
        npc_weakened: combatEffects.debuffs.npcWeakened,
        ability_lock: combatEffects.debuffs.abilityLock,
        ability_lock_turns: combatEffects.debuffs.abilityLockShowTurns,
        deep_sleep: combatEffects.debuffs.deepSleep,
        npc_poison_debuff: combatEffects.debuffs.npcPoison,

        // Injury Debuffs
        damage_debuff: combatEffects.debuffs.damage,
        bleed_debuff: combatEffects.debuffs.bleed,
        defence_debuff: combatEffects.debuffs.defence,
        accuracy_debuff: combatEffects.debuffs.accuracy,
        stamina_regen_debuff: combatEffects.debuffs.staminaRegen,
        ability_lock_debuff: combatEffects.debuffs.injuryAbilityLock,
        ability_damage_debuff: combatEffects.debuffs.abilityDamage,
        zombified_debuff: combatEffects.debuffs.zombie,
        fever_debuff: combatEffects.debuffs.fever,
    };
    return battleStatusEffects[key];
};

const effects = {
    buffs: {},
    // Injury Debuffs
    debuffs: {
        abilityLock: {
            icon: abilityLockImg,
        },
        bleed: {
            icon: bleedImg,
        },
        damage: {
            icon: painImg,
        },
        defence: {
            icon: disarmedImg,
        },
        staminaRegen: {
            icon: fatigueImg,
        },
        accuracy: {
            icon: concussionImg,
        },
        abilityDamage: {
            icon: traumaImg,
        },
        zombie: {
            icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/WWSOgR8.png`,
        },
        fever: {
            icon: `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/2LYGQTA.png`,
        },
    },
};

export const statusEffects = {
    // Out of combat Injury Debuffs
    fracture_injury: effects.debuffs.damage,
    bleeding_injury: effects.debuffs.bleed,
    muscle_injury: effects.debuffs.defence,
    concussion_injury: effects.debuffs.accuracy,
    stamina_regen_debuff: effects.debuffs.staminaRegen,
    fatigue_injury: effects.debuffs.staminaRegen,
    ability_damage_debuff: effects.debuffs.abilityDamage,
    zombified_debuff: effects.debuffs.zombie,
    fever_debuff: effects.debuffs.fever,
};

export const getModifierText = (modifier: number, type: string) => {
    if (!modifier) return null;
    switch (type) {
        case "damage_debuff":
            return `-${(modifier * 100).toFixed(0)}% Damage`;
        case "ability_damage_debuff":
            return `-${(modifier * 100).toFixed(0)}% Ability Damage`;
        case "bleed_debuff":
            return `Taking ${(modifier * 100).toFixed(0)}% Max HP damage each combat turn`;
        case "defence_debuff":
            return `-${(modifier * 100).toFixed(0)}% Defence`;
        case "accuracy_debuff":
            return `+${(modifier * 100).toFixed(0)}% chance to miss attacks`;
        case "stamina_regen_debuff":
            return `-100% Stamina Regen in combat`;
        case "stamina_debuff":
            return `-${(modifier * 100).toFixed(0)}% max Stamina in combat`;
        case "ability_lock_debuff":
            return `Can't use abilities in Combat`;
        case "zombified_debuff":
            return `-${(modifier * 100).toFixed(0)}% Defence`;
        case "fever_debuff":
            return `A raging Headache`;
        default:
            return null;
    }
};
